import { Image } from 'expo-image';
import { useState } from 'react';
import { Platform, StyleSheet, Alert } from 'react-native';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import CustomTextField from '@/components/ui/CustomTextField';
import { useRouter } from 'expo-router';
import Config from '@/config';
import { useAuthentication } from '@/hooks/useAuthentication';

export default function HomeScreen() {
	const [waveTrigger, setWaveTrigger] = useState(0);
	const router = useRouter();
	const { user, signOut } = useAuthentication();

	const handleSignOut = async () => {
		Alert.alert(
			'Sign Out',
			'Are you sure you want to sign out?',
			[
				{ text: 'Cancel', style: 'cancel' },
				{
					text: 'Sign Out',
					style: 'destructive',
					onPress: async () => {
						try {
							await signOut();
							router.replace('/auth');
						} catch (error) {
							Alert.alert('Error', 'Failed to sign out. Please try again.');
						}
					},
				},
			]
		);
	};

	return (
		<ParallaxScrollView
			headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
			headerImage={
				<Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
			}
		>
			<ThemedView style={styles.titleContainer}>
				<CustomTextField variant='primary'>
					Welcome{user ? `, ${user.name.split(' ')[0]}` : ''}
				</CustomTextField>
				<HelloWave waveTrigger={waveTrigger} />
				<CustomButton variant='primary' onPress={() => setWaveTrigger((prev) => prev + 1)}>
					Wave Again
				</CustomButton>
			</ThemedView>

			{user && (
				<ThemedView style={styles.stepContainer}>
					<ThemedText type='title'>Account</ThemedText>
					<ThemedText>Signed in as: {user.email}</ThemedText>
					<ThemedText>Roles: {user.roles.join(', ') || 'User'}</ThemedText>
					<CustomButton
						variant='secondary'
						onPress={handleSignOut}
					>
						Sign Out
					</CustomButton>
				</ThemedView>
			)}
			<ThemedView style={styles.stepContainer}>
				<ThemedText type='title'>Config</ThemedText>
				<ThemedText>{Config.ENV_NAME}</ThemedText>
				<ThemedText>{Config.API_URL}</ThemedText>
				<ThemedText>{Config.CONFIG_VERSION}</ThemedText>
			</ThemedView>
			<ThemedView style={styles.stepContainer}>
				<ThemedText type='title'>Navigation</ThemedText>
				<CustomButton
					variant='primary'
					onPress={() => {
						router.push('hello');
					}}
				>
					Click to navigate
				</CustomButton>
			</ThemedView>
		</ParallaxScrollView>
	);
}

const styles = StyleSheet.create({
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: 8,
	},
	stepContainer: {
		gap: 8,
		marginBottom: 8,
	},
	reactLogo: {
		height: 48,
		width: 96,
		top: 30,
		left: 20,
		position: 'absolute',
	},
});
