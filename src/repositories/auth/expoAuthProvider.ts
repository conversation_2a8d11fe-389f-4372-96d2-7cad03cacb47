/**
 * Expo Authentication Provider
 * 
 * Implementation of authentication for mobile platforms using expo-auth-session.
 * Handles OIDC authentication with PKCE flow for secure mobile authentication.
 */

import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { IExpoAuthProvider } from './interfaces';
import { AuthenticationData, AuthenticatedUser, AuthConfig } from '@/types/auth';

// Complete auth session when returning to app
WebBrowser.maybeCompleteAuthSession();

/**
 * Expo implementation of authentication using expo-auth-session
 */
export class ExpoAuthProvider implements IExpoAuthProvider {
  private discovery: AuthSession.DiscoveryDocument;
  private clientId: string;
  private redirectUri: string;
  private scopes: string[];
  private currentRequest: AuthSession.AuthRequest | null = null;

  constructor(config?: Partial<AuthConfig>) {
    // Use environment variables with config overrides
    this.clientId = config?.clientId || process.env.EXPO_PUBLIC_OKTA_CLIENT_ID!;
    this.scopes = config?.scopes || ['openid', 'profile', 'email', 'offline_access'];

    // Use Expo auth proxy for consistent redirect handling
    const redirectUriProxy = "https://auth.expo.dev/@charlesrmajor/learning-coach-community";
    this.redirectUri = config?.redirectUri || redirectUriProxy;

    if (!this.clientId) {
      throw new Error('Okta Client ID is required for Expo authentication');
    }

    const issuer = config?.issuer || process.env.EXPO_PUBLIC_OKTA_ISSUER!;
    if (!issuer) {
      throw new Error('Okta Issuer is required for Expo authentication');
    }

    // Set up discovery document for Okta OIDC endpoints
    this.discovery = {
      authorizationEndpoint: `${issuer}/v1/authorize`,
      tokenEndpoint: `${issuer}/v1/token`,
      revocationEndpoint: `${issuer}/v1/revoke`,
      userInfoEndpoint: `${issuer}/v1/userinfo`,
      endSessionEndpoint: `${issuer}/v1/logout`,
    };
  }

  /**
   * Initiate sign-in process using OIDC with PKCE
   */
  async signIn(): Promise<AuthenticationData> {
    try {
      // Create auth request with PKCE
      const request = new AuthSession.AuthRequest({
        clientId: this.clientId,
        scopes: this.scopes,
        redirectUri: this.redirectUri,
        responseType: AuthSession.ResponseType.Code,
        usePKCE: true,
        prompt: AuthSession.Prompt.SelectAccount,
      });

      this.currentRequest = request;

      // Prompt for authentication
      const result = await request.promptAsync(this.discovery);
      
      if (result.type !== 'success') {
        throw new Error(`Authentication failed: ${result.type}`);
      }

      if (!result.params.code) {
        throw new Error('No authorization code received');
      }

      // Exchange code for tokens
      return await this.exchangeCodeForTokens(result.params.code, request.codeVerifier!);
    } catch (error) {
      console.error('Sign in failed:', error);
      throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<void> {
    try {
      // Clear any stored tokens - this would be handled by the storage service
      // For now, we just log the sign out
      console.log('User signed out from Expo auth provider');
      
      // Note: Full logout from Okta would require opening a browser session
      // This is typically handled by clearing local tokens and optionally
      // redirecting to Okta's logout endpoint
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh the current authentication token
   */
  async refreshToken(): Promise<AuthenticationData> {
    try {
      // This would typically use a stored refresh token
      // For now, throw an error indicating implementation needed
      throw new Error('Token refresh not implemented - requires stored refresh token');
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw new Error(`Token refresh failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      // This would check stored auth data and expiration
      // For now, return false as a placeholder
      return false;
    } catch (error) {
      console.error('Authentication check failed:', error);
      return false;
    }
  }

  /**
   * Get current authenticated user information
   */
  async getCurrentUser(): Promise<AuthenticatedUser | null> {
    try {
      const accessToken = await this.getAccessToken();
      if (!accessToken) {
        return null;
      }

      // Fetch user info from OIDC userinfo endpoint
      const response = await fetch(this.discovery.userInfoEndpoint!, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user info: ${response.status} ${response.statusText}`);
      }

      const userInfo = await response.json();
      
      return {
        id: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name || `${userInfo.given_name || ''} ${userInfo.family_name || ''}`.trim(),
        roles: userInfo.groups || [],
        tenant: userInfo.tenant,
        groups: userInfo.groups,
        attributes: {
          given_name: userInfo.given_name,
          family_name: userInfo.family_name,
          preferred_username: userInfo.preferred_username,
        },
      };
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      // This would retrieve from secure storage
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      // This would retrieve from secure storage
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      const expiration = await this.getTokenExpiration();
      if (!expiration) {
        return true;
      }
      
      return Date.now() >= expiration;
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpiration(): Promise<number | null> {
    try {
      // This would retrieve from stored auth data
      // For now, return null as placeholder
      return null;
    } catch (error) {
      console.error('Failed to get token expiration:', error);
      return null;
    }
  }

  /**
   * Create redirect URI for the current platform
   */
  makeRedirectUri(): string {
    return AuthSession.makeRedirectUri({
      scheme: process.env.EXPO_PUBLIC_APP_SCHEME || 'com.company.app',
      path: 'callback',
    });
  }

  /**
   * Get authentication request configuration
   */
  getAuthRequestConfig(): any {
    return {
      clientId: this.clientId,
      discovery: this.discovery,
      redirectUri: this.redirectUri,
      scopes: this.scopes,
    };
  }

  /**
   * Create and configure auth request
   */
  async createAuthRequest(config?: Partial<AuthConfig>): Promise<AuthSession.AuthRequest> {
    const mergedConfig = {
      clientId: this.clientId,
      scopes: this.scopes,
      redirectUri: this.redirectUri,
      ...config,
    };

    return new AuthSession.AuthRequest({
      clientId: mergedConfig.clientId,
      scopes: mergedConfig.scopes,
      redirectUri: mergedConfig.redirectUri,
      responseType: AuthSession.ResponseType.Code,
      usePKCE: true,
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string, codeVerifier: string): Promise<AuthenticationData> {
    try {
      const tokenResult = await AuthSession.exchangeCodeAsync(
        {
          clientId: this.clientId,
          code,
          redirectUri: this.redirectUri,
          extraParams: {
            code_verifier: codeVerifier,
          },
        },
        this.discovery
      );

      if (!tokenResult.accessToken) {
        throw new Error('No access token received from token exchange');
      }

      return {
        accessToken: tokenResult.accessToken,
        refreshToken: tokenResult.refreshToken || undefined,
        idToken: tokenResult.idToken || undefined,
        expiresAt: Date.now() + (tokenResult.expiresIn! * 1000),
        tokenType: 'Bearer',
        scope: tokenResult.scope?.split(' ') || this.scopes,
      };
    } catch (error) {
      console.error('Token exchange failed:', error);
      throw new Error(`Token exchange failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Open browser for authentication
   */
  async openAuthSession(authUrl: string): Promise<any> {
    try {
      return await WebBrowser.openAuthSessionAsync(authUrl, this.redirectUri);
    } catch (error) {
      console.error('Auth session failed:', error);
      throw new Error(`Auth session failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle deep link callback
   */
  async handleDeepLink(url: string): Promise<AuthenticationData | null> {
    try {
      // Parse the URL to extract authorization code
      const urlObj = new URL(url);
      const code = urlObj.searchParams.get('code');
      
      if (!code) {
        console.warn('No authorization code found in deep link');
        return null;
      }

      if (!this.currentRequest?.codeVerifier) {
        throw new Error('No code verifier available for token exchange');
      }

      return await this.exchangeCodeForTokens(code, this.currentRequest.codeVerifier);
    } catch (error) {
      console.error('Deep link handling failed:', error);
      return null;
    }
  }
}
