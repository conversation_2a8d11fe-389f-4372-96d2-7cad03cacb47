/**
 * Authentication Service
 * 
 * Business logic layer for authentication operations.
 * Orchestrates authentication providers and storage repositories to provide
 * a consistent authentication API across platforms.
 */

import { IAuthenticationProvider } from '@/repositories/auth/interfaces';
import { IAuthStorageRepository, ISessionStorageRepository } from '@/repositories/interfaces';
import { 
  AuthenticationState, 
  AuthenticatedUser, 
  AuthenticationData, 
  AuthResult, 
  TokenRefreshResult,
  AuthEvent,
  AuthEventData 
} from '@/types/auth';

/**
 * Authentication service providing business logic for auth operations
 */
export class AuthenticationService {
  private eventListeners: Map<AuthEvent, ((data: AuthEventData) => void)[]> = new Map();

  constructor(
    private authProvider: IAuthenticationProvider,
    private storageRepository: IAuthStorageRepository,
    private sessionRepository?: ISessionStorageRepository
  ) {
    // Initialize event listeners map
    Object.values(AuthEvent).forEach(event => {
      this.eventListeners.set(event, []);
    });
  }

  /**
   * Sign in user and store authentication data
   */
  async signIn(): Promise<AuthenticationState> {
    try {
      this.emitEvent(AuthEvent.SIGN_IN_SUCCESS, { event: AuthEvent.SIGN_IN_SUCCESS, timestamp: Date.now() });

      // Perform authentication
      const authData = await this.authProvider.signIn();
      
      // Validate authentication data
      this.validateAuthenticationData(authData);

      // Get user profile
      const user = await this.authProvider.getCurrentUser();
      
      if (user) {
        // Store authentication data and user profile
        await Promise.all([
          this.storageRepository.saveAuthData(authData),
          this.storageRepository.saveUserProfile(user)
        ]);

        this.emitEvent(AuthEvent.SIGN_IN_SUCCESS, { 
          event: AuthEvent.SIGN_IN_SUCCESS, 
          user, 
          timestamp: Date.now() 
        });

        return {
          isAuthenticated: true,
          user,
          isLoading: false,
          error: null
        };
      } else {
        throw new Error('Failed to retrieve user profile after authentication');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      
      this.emitEvent(AuthEvent.SIGN_IN_FAILURE, { 
        event: AuthEvent.SIGN_IN_FAILURE, 
        error: errorMessage, 
        timestamp: Date.now() 
      });

      return {
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: errorMessage
      };
    }
  }

  /**
   * Sign out user and clear all authentication data
   */
  async signOut(): Promise<void> {
    try {
      // Sign out from auth provider
      await this.authProvider.signOut();

      // Clear stored authentication data
      await this.storageRepository.clearAuthData();

      // Clear session data if available
      if (this.sessionRepository) {
        await this.sessionRepository.clearSession();
      }

      this.emitEvent(AuthEvent.SIGN_OUT, { 
        event: AuthEvent.SIGN_OUT, 
        timestamp: Date.now() 
      });
    } catch (error) {
      console.error('Sign out failed:', error);
      throw new Error(`Sign out failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Refresh authentication tokens if needed
   */
  async refreshAuthIfNeeded(bufferMinutes: number = 5): Promise<TokenRefreshResult> {
    try {
      const authData = await this.storageRepository.getAuthData();
      if (!authData) {
        return { success: false, error: 'No authentication data found' };
      }

      // Check if token expires within buffer time
      const bufferMs = bufferMinutes * 60 * 1000;
      const needsRefresh = authData.expiresAt <= (Date.now() + bufferMs);

      if (needsRefresh) {
        const newAuthData = await this.authProvider.refreshToken();
        this.validateAuthenticationData(newAuthData);

        // Store new authentication data
        await this.storageRepository.saveAuthData(newAuthData);

        this.emitEvent(AuthEvent.TOKEN_REFRESH_SUCCESS, { 
          event: AuthEvent.TOKEN_REFRESH_SUCCESS, 
          timestamp: Date.now() 
        });

        return { success: true, authData: newAuthData };
      }

      return { success: true, authData };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Token refresh failed';
      
      this.emitEvent(AuthEvent.TOKEN_REFRESH_FAILURE, { 
        event: AuthEvent.TOKEN_REFRESH_FAILURE, 
        error: errorMessage, 
        timestamp: Date.now() 
      });

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Get current authentication state
   */
  async getCurrentAuthState(): Promise<AuthenticationState> {
    try {
      // Check if authenticated via provider
      const isAuthenticated = await this.authProvider.isAuthenticated();
      
      if (!isAuthenticated) {
        return {
          isAuthenticated: false,
          user: null,
          isLoading: false,
          error: null
        };
      }

      // Get stored user profile
      const user = await this.storageRepository.getUserProfile();

      // If no stored user, try to get from provider
      if (!user) {
        const providerUser = await this.authProvider.getCurrentUser();
        if (providerUser) {
          await this.storageRepository.saveUserProfile(providerUser);
          return {
            isAuthenticated: true,
            user: providerUser,
            isLoading: false,
            error: null
          };
        }
      }

      return {
        isAuthenticated: true,
        user,
        isLoading: false,
        error: null
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get auth state';
      return {
        isAuthenticated: false,
        user: null,
        isLoading: false,
        error: errorMessage
      };
    }
  }

  /**
   * Get current access token
   */
  async getAccessToken(): Promise<string | null> {
    try {
      return await this.authProvider.getAccessToken();
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Get current ID token
   */
  async getIdToken(): Promise<string | null> {
    try {
      return await this.authProvider.getIdToken();
    } catch (error) {
      console.error('Failed to get ID token:', error);
      return null;
    }
  }

  /**
   * Check if current session is valid
   */
  async isSessionValid(): Promise<boolean> {
    try {
      const isAuthenticated = await this.authProvider.isAuthenticated();
      if (!isAuthenticated) {
        return false;
      }

      const isExpired = await this.authProvider.isTokenExpired();
      return !isExpired;
    } catch (error) {
      console.error('Failed to check session validity:', error);
      return false;
    }
  }

  /**
   * Handle session expiration
   */
  async handleSessionExpiration(): Promise<void> {
    try {
      // Clear stored data
      await this.storageRepository.clearAuthData();
      
      if (this.sessionRepository) {
        await this.sessionRepository.clearSession();
      }

      this.emitEvent(AuthEvent.SESSION_EXPIRED, { 
        event: AuthEvent.SESSION_EXPIRED, 
        timestamp: Date.now() 
      });
    } catch (error) {
      console.error('Failed to handle session expiration:', error);
    }
  }

  /**
   * Add event listener for authentication events
   */
  addEventListener(event: AuthEvent, listener: (data: AuthEventData) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.push(listener);
    }
  }

  /**
   * Remove event listener for authentication events
   */
  removeEventListener(event: AuthEvent, listener: (data: AuthEventData) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Validate authentication data
   */
  private validateAuthenticationData(authData: AuthenticationData): void {
    if (!authData.accessToken) {
      throw new Error('Authentication data must include access token');
    }

    if (!authData.expiresAt || authData.expiresAt <= Date.now()) {
      throw new Error('Authentication data must include valid expiration time');
    }

    if (authData.tokenType !== 'Bearer') {
      throw new Error('Only Bearer token type is supported');
    }
  }

  /**
   * Emit authentication event to listeners
   */
  private emitEvent(event: AuthEvent, data: AuthEventData): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`Error in auth event listener for ${event}:`, error);
        }
      });
    }
  }
}
