/**
 * Service Factory - Dependency Injection Container
 * 
 * Provides a clean factory pattern for creating services with proper dependency injection.
 * Handles platform detection and creates appropriate implementations for each platform.
 */

import { Platform } from 'react-native';
import { AuthenticationService } from './authenticationService';
import { SecureStorageService } from './secureStorageService';

// Repository interfaces
import { IAuthenticationProvider } from '@/repositories/auth/interfaces';
import { IAuthStorageRepository, ISessionStorageRepository } from '@/repositories/interfaces';

// Mobile implementations
import { MobileSecureStorageRepository } from '@/repositories/secureStorage/mobileSecureStorageRepository';
import { ExpoAuthProvider } from '@/repositories/auth/expoAuthProvider';

// Web implementations
import { 
  WebSecureStorageRepository, 
  WebSessionStorageRepository, 
  WebPreferencesRepository 
} from '@/repositories/secureStorage/webSecureStorageRepository';
import { OktaWebAuthProvider } from '@/repositories/auth/oktaWebAuthProvider';

// Types
import { AuthConfig } from '@/types/auth';

/**
 * Service factory class for dependency injection and service creation
 */
export class ServiceFactory {
  private static authService: AuthenticationService | null = null;
  private static storageService: SecureStorageService | null = null;
  private static preferencesRepository: WebPreferencesRepository | null = null;

  /**
   * Create authentication service with platform-appropriate dependencies
   */
  static createAuthenticationService(config?: Partial<AuthConfig>): AuthenticationService {
    if (!this.authService) {
      const authProvider = this.createAuthProvider(config);
      const storageRepository = this.createAuthStorageRepository();
      const sessionRepository = Platform.OS === 'web' 
        ? this.createSessionStorageRepository() 
        : undefined;

      this.authService = new AuthenticationService(
        authProvider,
        storageRepository,
        sessionRepository
      );
    }
    return this.authService;
  }

  /**
   * Create secure storage service with platform-appropriate dependencies
   */
  static createSecureStorageService(): SecureStorageService {
    if (!this.storageService) {
      const storageRepository = this.createSecureStorageRepository();
      this.storageService = new SecureStorageService(storageRepository);
    }
    return this.storageService;
  }

  /**
   * Create preferences repository (web only)
   */
  static createPreferencesRepository(): WebPreferencesRepository {
    if (Platform.OS !== 'web') {
      throw new Error('Preferences repository is only available on web platform');
    }

    if (!this.preferencesRepository) {
      this.preferencesRepository = new WebPreferencesRepository();
    }
    return this.preferencesRepository;
  }

  /**
   * Get current platform name
   */
  static getCurrentPlatform(): string {
    return Platform.OS;
  }

  /**
   * Check if current platform is mobile
   */
  static isMobilePlatform(): boolean {
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  /**
   * Check if current platform is web
   */
  static isWebPlatform(): boolean {
    return Platform.OS === 'web';
  }

  /**
   * Create authentication provider based on platform
   */
  private static createAuthProvider(config?: Partial<AuthConfig>): IAuthenticationProvider {
    if (this.isWebPlatform()) {
      return new OktaWebAuthProvider(config);
    } else if (this.isMobilePlatform()) {
      return new ExpoAuthProvider(config);
    } else {
      throw new Error(`Unsupported platform: ${Platform.OS}`);
    }
  }

  /**
   * Create auth storage repository based on platform
   */
  private static createAuthStorageRepository(): IAuthStorageRepository {
    if (this.isWebPlatform()) {
      return new WebSecureStorageRepository();
    } else if (this.isMobilePlatform()) {
      return new MobileSecureStorageRepository();
    } else {
      throw new Error(`Unsupported platform: ${Platform.OS}`);
    }
  }

  /**
   * Create secure storage repository based on platform
   */
  private static createSecureStorageRepository(): IAuthStorageRepository {
    return this.createAuthStorageRepository();
  }

  /**
   * Create session storage repository (web only)
   */
  private static createSessionStorageRepository(): ISessionStorageRepository {
    if (!this.isWebPlatform()) {
      throw new Error('Session storage is only available on web platform');
    }
    return new WebSessionStorageRepository();
  }

  /**
   * Create services for testing with mock dependencies
   */
  static createTestAuthenticationService(
    authProvider: IAuthenticationProvider,
    storageRepository: IAuthStorageRepository,
    sessionRepository?: ISessionStorageRepository
  ): AuthenticationService {
    return new AuthenticationService(authProvider, storageRepository, sessionRepository);
  }

  /**
   * Create secure storage service for testing with mock dependencies
   */
  static createTestSecureStorageService(
    storageRepository: IAuthStorageRepository
  ): SecureStorageService {
    return new SecureStorageService(storageRepository);
  }

  /**
   * Reset all singleton instances (useful for testing)
   */
  static reset(): void {
    this.authService = null;
    this.storageService = null;
    this.preferencesRepository = null;
  }

  /**
   * Get service configuration info
   */
  static getServiceInfo(): {
    platform: string;
    authProvider: string;
    storageProvider: string;
    hasSessionStorage: boolean;
    hasPreferencesStorage: boolean;
  } {
    return {
      platform: Platform.OS,
      authProvider: this.isWebPlatform() ? 'OktaWebAuthProvider' : 'ExpoAuthProvider',
      storageProvider: this.isWebPlatform() ? 'WebSecureStorageRepository' : 'MobileSecureStorageRepository',
      hasSessionStorage: this.isWebPlatform(),
      hasPreferencesStorage: this.isWebPlatform(),
    };
  }

  /**
   * Validate environment configuration
   */
  static validateEnvironment(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required environment variables
    if (!process.env.EXPO_PUBLIC_OKTA_ISSUER) {
      errors.push('EXPO_PUBLIC_OKTA_ISSUER environment variable is required');
    }

    if (!process.env.EXPO_PUBLIC_OKTA_CLIENT_ID) {
      errors.push('EXPO_PUBLIC_OKTA_CLIENT_ID environment variable is required');
    }

    // Platform-specific checks
    if (this.isMobilePlatform()) {
      if (!process.env.EXPO_PUBLIC_APP_SCHEME) {
        warnings.push('EXPO_PUBLIC_APP_SCHEME not set, using default scheme');
      }
    }

    // Check if we're in a browser environment for web platform
    if (this.isWebPlatform() && typeof window === 'undefined') {
      warnings.push('Web platform detected but window object is not available (SSR?)');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * Initialize services and perform startup checks
   */
  static async initialize(): Promise<{
    success: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const validation = this.validateEnvironment();
    
    if (!validation.isValid) {
      return {
        success: false,
        errors: validation.errors,
        warnings: validation.warnings,
      };
    }

    try {
      // Pre-create services to catch any initialization errors
      const authService = this.createAuthenticationService();
      const storageService = this.createSecureStorageService();

      // Test storage availability
      const storageInfo = await storageService.getStorageInfo();
      if (!storageInfo.success || !storageInfo.data?.isAvailable) {
        validation.warnings.push('Storage may not be available on this platform');
      }

      return {
        success: true,
        errors: [],
        warnings: validation.warnings,
      };
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown initialization error'],
        warnings: validation.warnings,
      };
    }
  }
}

/**
 * Default singleton instances - use these for most application needs
 */
export const authenticationService = ServiceFactory.createAuthenticationService();
export const secureStorageService = ServiceFactory.createSecureStorageService();

/**
 * Export factory as default for custom instantiation
 */
export { ServiceFactory as default };
